import type { AppLoadContext } from "@remix-run/cloudflare";
import { createAuth } from "@repo/auth";
import { type User, createDatabase } from "@repo/db";

// Initialize auth instance for Cloudflare Pages Functions
export function getAuth(context: AppLoadContext) {
  const env = (context.cloudflare as { env: Record<string, string> }).env;
  const db = createDatabase(env.DATABASE_URL);
  return createAuth(db, {
    BETTER_AUTH_SECRET: env.BETTER_AUTH_SECRET,
    GOOGLE_CLIENT_ID: env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: env.GOOGLE_CLIENT_SECRET,
  });
}

// Session utilities for Remix loaders
export async function getUser(request: Request, context: AppLoadContext) {
  const auth = getAuth(context);
  const session = await auth.api.getSession({
    headers: request.headers,
  });

  return session?.user || null;
}

export async function requireUser(request: Request, context: AppLoadContext) {
  const user = await getUser(request, context);
  if (!user) {
    throw new Response("Unauthorized", { status: 401 });
  }
  return user;
}

export async function requireRole(
  request: Request,
  context: AppLoadContext,
  requiredRole: "admin" | "user"
) {
  const user = await requireUser(request, context);

  // Use the role field from the database
  const userRole = (user as User).role || "user";

  if (requiredRole === "admin" && userRole !== "admin") {
    throw new Response("Forbidden", { status: 403 });
  }

  return { user, role: userRole };
}

export async function logout(request: Request, context: AppLoadContext) {
  const auth = getAuth(context);
  await auth.api.signOut({
    headers: request.headers,
  });
}
