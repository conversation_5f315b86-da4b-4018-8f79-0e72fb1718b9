# Cloudflare Worker Development Environment Variables
# Copy this file to .dev.vars and fill in your actual values

# Database
DATABASE_URL=********************************************************************

# Authentication
AUTH_SECRET=your-32-character-secret-key-here
AUTH_GITHUB_ID=your-github-oauth-app-id
AUTH_GITHUB_SECRET=your-github-oauth-app-secret

# Storage (if using Cloudflare R2)
R2_ACCESS_KEY_ID=your-r2-access-key
R2_SECRET_ACCESS_KEY=your-r2-secret-key

# Analytics (if using)
ANALYTICS_API_KEY=your-analytics-api-key

# Other API Keys
OPENAI_API_KEY=your-openai-api-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key