import { eq, and } from "drizzle-orm";
import type { NeonHttpDatabase } from "drizzle-orm/neon-http";
import { accounts, users, accountMemberships, type AppPermission, type AccountRole } from "../schema";

/**
 * Database utility functions for permission checking and account management
 */

export class PermissionManager {
  constructor(private db: NeonHttpDatabase<any>) {}

  /**
   * Check if a user has a specific permission on an account
   */
  async hasPermission(
    userId: string,
    accountId: string,
    permission: AppPermission
  ): Promise<boolean> {
    try {
      const membership = await this.db
        .select()
        .from(accountMemberships)
        .where(
          and(
            eq(accountMemberships.userId, userId),
            eq(accountMemberships.accountId, accountId)
          )
        )
        .limit(1);

      if (!membership.length) return false;

      const userMembership = membership[0];
      if (!userMembership) return false;
      
      // Owners and admins have all permissions
      if (userMembership.role === "owner" || userMembership.role === "admin") {
        return true;
      }

      // Check specific permissions - note: permissions is stored as text array in the DB
      if (!userMembership.permissions) return false;
      
      // If permissions is a string (JSON), parse it; otherwise it's already an array
      const permissions = Array.isArray(userMembership.permissions) 
        ? userMembership.permissions 
        : JSON.parse(userMembership.permissions as any) as string[];
        
      return permissions.includes(permission);
    } catch (error) {
      console.error("Error checking permission:", error);
      return false;
    }
  }

  /**
   * Check if a user has a specific role on an account
   */
  async hasRoleOnAccount(
    userId: string,
    accountId: string,
    role: AccountRole
  ): Promise<boolean> {
    try {
      const membership = await this.db
        .select()
        .from(accountMemberships)
        .where(
          and(
            eq(accountMemberships.userId, userId),
            eq(accountMemberships.accountId, accountId),
            eq(accountMemberships.role, role)
          )
        )
        .limit(1);

      return membership.length > 0;
    } catch (error) {
      console.error("Error checking role:", error);
      return false;
    }
  }

  /**
   * Get all accounts a user has access to
   */
  async getUserAccounts(userId: string) {
    try {
      return await this.db
        .select({
          account: accounts,
          membership: accountMemberships,
        })
        .from(accountMemberships)
        .innerJoin(accounts, eq(accounts.id, accountMemberships.accountId))
        .where(eq(accountMemberships.userId, userId));
    } catch (error) {
      console.error("Error getting user accounts:", error);
      return [];
    }
  }

  /**
   * Create a personal account for a new user
   */
  async createPersonalAccount(userId: string, name: string, email: string) {
    const slug = email.split("@")[0]?.toLowerCase().replace(/[^a-z0-9]/g, "-") || "account";
    
    try {
      // Create account
      const [account] = await this.db
        .insert(accounts)
        .values({
          name: `${name}'s Account`,
          type: "personal",
          slug,
        })
        .returning();

      if (!account) {
        throw new Error("Failed to create account");
      }

      // Create membership
      await this.db.insert(accountMemberships).values({
        accountId: account.id,
        userId,
        role: "owner",
        permissions: [], // Owners have all permissions by default
      });

      // Update user's primary account
      await this.db
        .update(users)
        .set({ primaryAccountId: account.id })
        .where(eq(users.id, userId));

      return account;
    } catch (error) {
      console.error("Error creating personal account:", error);
      throw error;
    }
  }

  /**
   * Add a user to an account with specific role and permissions
   */
  async addUserToAccount(
    accountId: string,
    userId: string,
    role: AccountRole,
    permissions: AppPermission[] = [],
    invitedBy?: string
  ) {
    try {
      return await this.db.insert(accountMemberships).values({
        accountId,
        userId,
        role,
        permissions,
        invitedBy,
      });
    } catch (error) {
      console.error("Error adding user to account:", error);
      throw error;
    }
  }

  /**
   * Remove a user from an account
   */
  async removeUserFromAccount(accountId: string, userId: string) {
    try {
      return await this.db
        .delete(accountMemberships)
        .where(
          and(
            eq(accountMemberships.accountId, accountId),
            eq(accountMemberships.userId, userId)
          )
        );
    } catch (error) {
      console.error("Error removing user from account:", error);
      throw error;
    }
  }

  /**
   * Update user's role and permissions on an account
   */
  async updateUserRole(
    accountId: string,
    userId: string,
    role: AccountRole,
    permissions: AppPermission[] = []
  ) {
    try {
      return await this.db
        .update(accountMemberships)
        .set({ role, permissions })
        .where(
          and(
            eq(accountMemberships.accountId, accountId),
            eq(accountMemberships.userId, userId)
          )
        );
    } catch (error) {
      console.error("Error updating user role:", error);
      throw error;
    }
  }
}

/**
 * Default permissions for each role
 */
export const ROLE_PERMISSIONS: Record<AccountRole, AppPermission[]> = {
  owner: [
    "accounts.read",
    "accounts.write", 
    "accounts.delete",
    "users.read",
    "users.write",
    "users.delete",
    "billing.read",
    "billing.write",
    "settings.read",
    "settings.write",
  ],
  admin: [
    "accounts.read",
    "accounts.write",
    "users.read",
    "users.write", 
    "billing.read",
    "billing.write",
    "settings.read",
    "settings.write",
  ],
  member: [
    "accounts.read",
    "users.read",
    "billing.read",
    "settings.read",
  ],
  viewer: [
    "accounts.read",
    "users.read",
  ],
};