import type { NeonHttpDatabase } from "drizzle-orm/neon-http";

/**
 * Authentication context utilities for Row Level Security
 */

export class AuthContext {
  constructor(private db: NeonHttpDatabase<any>) {}

  /**
   * Set the current user ID for RLS policies
   * This should be called after authentication in your middleware
   */
  async setCurrentUser(userId: string): Promise<void> {
    await this.db.execute(`SET app.current_user_id = '${userId}'`);
  }

  /**
   * Clear the current user context
   */
  async clearCurrentUser(): Promise<void> {
    await this.db.execute(`SET app.current_user_id = ''`);
  }

  /**
   * Execute a function with a specific user context
   */
  async withUser<T>(userId: string, fn: () => Promise<T>): Promise<T> {
    await this.setCurrentUser(userId);
    try {
      return await fn();
    } finally {
      await this.clearCurrentUser();
    }
  }

  /**
   * Get the current user ID from the database context
   */
  async getCurrentUserId(): Promise<string | null> {
    try {
      const result = await this.db.execute(
        `SELECT current_setting('app.current_user_id', true) as user_id`
      );
      const row = result.rows[0] as { user_id: string } | undefined;
      const userId = row?.user_id;
      return userId && userId !== '' ? userId : null;
    } catch {
      return null;
    }
  }
}

/**
 * Middleware helper for Remix loaders and actions
 */
export function createAuthenticatedDatabase(
  db: NeonHttpDatabase<any>,
  userId: string
) {
  const authContext = new AuthContext(db);
  
  // Return a wrapped database that automatically sets the user context
  return {
    ...db,
    async query(...args: any[]) {
      await authContext.setCurrentUser(userId);
      try {
        return await (db as any).query(...args);
      } finally {
        await authContext.clearCurrentUser();
      }
    },
    async execute(...args: any[]) {
      await authContext.setCurrentUser(userId);
      try {
        return await (db as any).execute(...args);
      } finally {
        await authContext.clearCurrentUser();
      }
    },
    select: (...args: any[]) => {
      // For select queries, we need to wrap the execution
      const query = (db as any).select(...args);
      const originalExecute = query.execute;
      
      query.execute = async () => {
        await authContext.setCurrentUser(userId);
        try {
          return await originalExecute.call(query);
        } finally {
          await authContext.clearCurrentUser();
        }
      };
      
      return query;
    },
    // Add other query methods as needed
  };
}