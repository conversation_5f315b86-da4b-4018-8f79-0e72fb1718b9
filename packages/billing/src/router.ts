import type { BillingProvider } from "./types.js";

/**
 * Payment gateway router that abstracts different billing providers
 */

export type BillingProviderType = "stripe" | "lemonsqueezy" | "paddle";

export interface BillingConfig {
  provider: BillingProviderType;
  apiKey: string;
  webhookSecret: string;
  environment?: "test" | "live";
  [key: string]: any;
}

export class BillingRouter {
  private provider: BillingProvider | null = null;
  private config: BillingConfig;
  private providerPromise: Promise<BillingProvider>;

  constructor(config: BillingConfig) {
    this.config = config;
    this.providerPromise = this.createProvider(config);
  }

  private async createProvider(config: BillingConfig): Promise<BillingProvider> {
    switch (config.provider) {
      case "stripe":
        return this.createStripeProvider(config);
      case "lemonsqueezy":
        return this.createLemonSqueezyProvider(config);
      case "paddle":
        throw new Error("Paddle provider not implemented yet");
      default:
        throw new Error(`Unsupported billing provider: ${config.provider}`);
    }
  }

  private async createStripeProvider(config: BillingConfig): Promise<BillingProvider> {
    const { StripeProvider } = await import("./providers/stripe.js");
    return new StripeProvider({
      apiKey: config.apiKey,
      environment: config.environment,
    });
  }

  private async createLemonSqueezyProvider(config: BillingConfig): Promise<BillingProvider> {
    // This will be implemented in the LemonSqueezy adapter
    throw new Error("LemonSqueezy provider will be implemented in lemonsqueezy.ts");
  }

  private async getProvider(): Promise<BillingProvider> {
    if (!this.provider) {
      this.provider = await this.providerPromise;
    }
    return this.provider;
  }

  // Proxy all BillingProvider methods to the underlying provider
  async getProducts() {
    const provider = await this.getProvider();
    return provider.getProducts();
  }

  async getProduct(productId: string) {
    const provider = await this.getProvider();
    return provider.getProduct(productId);
  }

  async getPrices(productId?: string) {
    const provider = await this.getProvider();
    return provider.getPrices(productId);
  }

  async getPrice(priceId: string) {
    const provider = await this.getProvider();
    return provider.getPrice(priceId);
  }

  async createCustomer(data: { email: string; name?: string; metadata?: Record<string, string> }) {
    const provider = await this.getProvider();
    return provider.createCustomer(data);
  }

  async getCustomer(customerId: string) {
    const provider = await this.getProvider();
    return provider.getCustomer(customerId);
  }

  async updateCustomer(customerId: string, data: Parameters<BillingProvider['updateCustomer']>[1]) {
    const provider = await this.getProvider();
    return provider.updateCustomer(customerId, data);
  }

  async deleteCustomer(customerId: string) {
    const provider = await this.getProvider();
    return provider.deleteCustomer(customerId);
  }

  async createSubscription(data: Parameters<BillingProvider['createSubscription']>[0]) {
    const provider = await this.getProvider();
    return provider.createSubscription(data);
  }

  async getSubscription(subscriptionId: string) {
    const provider = await this.getProvider();
    return provider.getSubscription(subscriptionId);
  }

  async updateSubscription(subscriptionId: string, data: Parameters<BillingProvider['updateSubscription']>[1]) {
    const provider = await this.getProvider();
    return provider.updateSubscription(subscriptionId, data);
  }

  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd?: boolean) {
    const provider = await this.getProvider();
    return provider.cancelSubscription(subscriptionId, cancelAtPeriodEnd);
  }

  async getInvoices(customerId: string) {
    const provider = await this.getProvider();
    return provider.getInvoices(customerId);
  }

  async getInvoice(invoiceId: string) {
    const provider = await this.getProvider();
    return provider.getInvoice(invoiceId);
  }

  async createCheckoutSession(data: Parameters<BillingProvider['createCheckoutSession']>[0]) {
    const provider = await this.getProvider();
    return provider.createCheckoutSession(data);
  }

  async createCustomerPortalSession(customerId: string, returnUrl: string) {
    const provider = await this.getProvider();
    return provider.createCustomerPortalSession(customerId, returnUrl);
  }

  async constructWebhookEvent(payload: string, signature: string, secret?: string) {
    const provider = await this.getProvider();
    const webhookSecret = secret || this.config.webhookSecret;
    return provider.constructWebhookEvent(payload, signature, webhookSecret);
  }

  async reportUsage(subscriptionItemId: string, quantity: number, timestamp?: Date) {
    const provider = await this.getProvider();
    if (!provider.reportUsage) {
      throw new Error(`Usage reporting not supported by ${this.config.provider} provider`);
    }
    return provider.reportUsage(subscriptionItemId, quantity, timestamp);
  }

  // Utility methods
  getProviderType(): BillingProviderType {
    return this.config.provider;
  }

  getConfig(): BillingConfig {
    return { ...this.config };
  }
}

/**
 * Factory function to create a billing router from environment variables
 */
export function createBillingRouter(env: Record<string, string | undefined>): BillingRouter {
  const provider = env.BILLING_PROVIDER as BillingProviderType;
  
  if (!provider) {
    throw new Error("BILLING_PROVIDER environment variable is required");
  }

  const baseConfig: BillingConfig = {
    provider,
    apiKey: "",
    webhookSecret: "",
    environment: (env.NODE_ENV === "production" ? "live" : "test") as "test" | "live",
  };

  switch (provider) {
    case "stripe":
      return new BillingRouter({
        ...baseConfig,
        apiKey: env.STRIPE_SECRET_KEY || "",
        webhookSecret: env.STRIPE_WEBHOOK_SECRET || "",
      });
      
    case "lemonsqueezy":
      return new BillingRouter({
        ...baseConfig,
        apiKey: env.LEMONSQUEEZY_API_KEY || "",
        webhookSecret: env.LEMONSQUEEZY_WEBHOOK_SECRET || "",
        storeId: env.LEMONSQUEEZY_STORE_ID || "",
      });
      
    default:
      throw new Error(`Unsupported billing provider: ${provider}`);
  }
}