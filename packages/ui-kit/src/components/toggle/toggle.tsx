import * as React from "react";
import { cn } from "../../utils/index.js";

/**
 * Toggle/Switch component for boolean inputs
 */

export interface ToggleProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  size?: "sm" | "md" | "lg";
  label?: string;
  description?: string;
}

export const Toggle = React.forwardRef<HTMLInputElement, ToggleProps>(
  ({ 
    className, 
    checked = false, 
    onChange, 
    size = "md", 
    label, 
    description, 
    disabled,
    ...props 
  }, ref) => {
    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      onChange?.(event.target.checked);
    };

    const sizeClasses = {
      sm: {
        switch: "h-4 w-7",
        thumb: "h-3 w-3 data-[state=checked]:translate-x-3",
      },
      md: {
        switch: "h-5 w-9",
        thumb: "h-4 w-4 data-[state=checked]:translate-x-4",
      },
      lg: {
        switch: "h-6 w-11",
        thumb: "h-5 w-5 data-[state=checked]:translate-x-5",
      },
    };

    const Component = (
      <label className={cn("flex items-center", disabled && "opacity-50 cursor-not-allowed")}>
        <div className="relative">
          <input
            ref={ref}
            type="checkbox"
            checked={checked}
            onChange={handleChange}
            disabled={disabled}
            className="sr-only"
            {...props}
          />
          <div
            className={cn(
              "relative inline-flex shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors",
              "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
              "disabled:cursor-not-allowed disabled:opacity-50",
              checked ? "bg-primary" : "bg-input",
              sizeClasses[size].switch,
              className
            )}
          >
            <span
              className={cn(
                "pointer-events-none block rounded-full bg-background shadow-lg ring-0 transition-transform",
                sizeClasses[size].thumb,
                checked && sizeClasses[size].thumb.includes("translate-x") 
                  ? sizeClasses[size].thumb.split(" ").find(c => c.includes("translate-x"))
                  : "translate-x-0"
              )}
            />
          </div>
        </div>
        {(label || description) && (
          <div className="ml-3">
            {label && (
              <div className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                {label}
              </div>
            )}
            {description && (
              <div className="text-xs text-muted-foreground mt-1">
                {description}
              </div>
            )}
          </div>
        )}
      </label>
    );

    return Component;
  }
);
Toggle.displayName = "Toggle";